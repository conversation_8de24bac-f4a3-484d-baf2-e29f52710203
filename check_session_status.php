<?php
/**
 * 检查会话状态
 */

// 数据库连接配置
$host = '127.0.0.1';
$dbname = 'ai_tool';
$username = 'root';
$password = 'rootroot';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔍 检查缓存中会话的数据库状态\n";
    echo "============================\n\n";
    
    // 当前日志中的会话ID
    $cacheSessionIds = [
        'ws_hc43IYQeHSQkHMmPyOzJBHz10AAjs7eF',
        'ws_rppoRAawFTen3AGlJxzvpJAirwV4IUlj',
        'ws_Ltr7Q67hzRt7YLwcEzcwSsEz9BZ5fqsP',
        'ws_ubMnpzBr8tPXY3D57mZQP2YEGG5jRndV'
    ];
    
    foreach ($cacheSessionIds as $sessionId) {
        $stmt = $pdo->prepare("SELECT session_id, status, last_ping_at, connected_at FROM p_websocket_sessions WHERE session_id = ?");
        $stmt->execute([$sessionId]);
        $session = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $shortId = substr($sessionId, 0, 25) . '...';
        
        if ($session) {
            echo "📋 {$shortId}:\n";
            echo "   状态: {$session['status']}\n";
            echo "   最后心跳: {$session['last_ping_at']}\n";
            echo "   连接时间: {$session['connected_at']}\n";
            
            if ($session['status'] !== 'connected') {
                echo "   ❌ 状态不是 'connected'，这会导致批量更新失败\n";
            } else {
                echo "   ✅ 状态正常\n";
            }
        } else {
            echo "❌ {$shortId}: 数据库中不存在\n";
        }
        echo "\n";
    }
    
    // 检查批量更新的SQL条件
    echo "🔍 模拟批量更新条件检查:\n";
    
    foreach ($cacheSessionIds as $sessionId) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM p_websocket_sessions WHERE session_id = ? AND status = 'connected'");
        $stmt->execute([$sessionId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $shortId = substr($sessionId, 0, 25) . '...';
        
        if ($result['count'] > 0) {
            echo "   ✅ {$shortId}: 符合更新条件\n";
        } else {
            echo "   ❌ {$shortId}: 不符合更新条件（状态不是connected或不存在）\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 检查失败: " . $e->getMessage() . "\n";
}
