<?php

return [
    /*
    |--------------------------------------------------------------------------
    | WebSocket服务配置
    |--------------------------------------------------------------------------
    |
    | WebSocket服务器的基础配置，包括连接URL、SSL证书路径等
    | 统一在此文件中进行配置管理，不再依赖环境变量
    |
    */

    // WebSocket连接URL
    'url' => 'wss://api.tiptop.cn:8080',

    // 服务器配置
    'server' => [
        'host' => '0.0.0.0',
        'port' => 8080,
        'mode' => 'legacy', // modern 或 legacy - 使用legacy模式启用SSL支持
    ],

    // SSL证书配置
    'ssl' => [
        'cert_file' => 'app/Console/Commands/ssl/api.tiptop.cn.pem',
        'key_file' => 'app/Console/Commands/ssl/api.tiptop.cn.key',
    ],
    
    // 服务器性能配置
    'performance' => [
        'worker_num' => 4,
        'max_connection' => 1000,
        'heartbeat_check_interval' => 30,
        'heartbeat_idle_time' => 60,
        'package_max_length' => 8192,
    ],

    // 会话管理配置
    'session' => [
        'timeout' => 120, // 2分钟
        'cleanup_interval' => 120, // 2分钟
        'max_connections_per_user' => 3,
    ],
    
    // 允许的客户端类型
    'allowed_clients' => [
        'python_tool' => true,
        'web_browser' => false, // 根据需求可以开启
        'mobile_app' => false,
    ],
    
    // 默认订阅事件
    'default_events' => [
        'python_tool' => [
            'ai_generation_progress',
            'ai_generation_completed', 
            'ai_generation_failed',
            'points_changed'
        ],
        'web_browser' => [
            'ai_generation_progress',
            'points_changed'
        ],
    ],
    
    // 日志配置
    'logging' => [
        'enabled' => true,
        'level' => 'info',
        'file' => 'websocket.log',
    ],
];
