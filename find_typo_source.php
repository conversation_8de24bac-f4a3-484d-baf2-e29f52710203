<?php
/**
 * 查找拼写错误的源头
 */

// 数据库连接配置
$host = '127.0.0.1';
$dbname = 'ai_tool';
$username = 'root';
$password = 'rootroot';

try {
    echo "🔍 查找 sessioon_id 拼写错误的源头\n";
    echo "============================\n\n";
    
    // 连接Redis查看具体的缓存数据
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    $redis->select(1); // 选择缓存数据库
    
    // 获取所有心跳缓存键
    $heartbeatKeys = $redis->keys('*websocket_heartbeat_*');
    
    if (!empty($heartbeatKeys)) {
        echo "📋 分析心跳缓存数据结构:\n";
        
        foreach ($heartbeatKeys as $key) {
            $data = $redis->get($key);
            if ($data) {
                echo "\n🔍 键: {$key}\n";
                echo "   原始数据: " . substr($data, 0, 200) . "\n";
                
                // 尝试反序列化
                $cacheData = unserialize($data);
                if ($cacheData) {
                    echo "   反序列化成功:\n";
                    foreach ($cacheData as $field => $value) {
                        echo "     • {$field}: {$value}\n";
                        
                        // 检查字段名是否有拼写错误
                        if ($field === 'sessioon_id') {
                            echo "     ❌ 发现拼写错误字段: {$field}\n";
                        }
                    }
                } else {
                    echo "   ❌ 反序列化失败\n";
                }
            }
        }
    }
    
    // 检查内存表可能的数据源
    echo "\n🔍 检查可能的数据源:\n";
    
    // 1. 检查是否有其他文件包含拼写错误
    $phpFiles = [
        'php/api/app/Console/Commands/WebSocketServer.php',
        'php/api/app/Services/PyApi/WebSocketService.php',
        'php/api/app/Models/WebSocketSession.php'
    ];
    
    foreach ($phpFiles as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            
            // 查找可能的拼写错误
            if (strpos($content, 'sessioon_id') !== false) {
                echo "   ❌ 文件 {$file} 包含拼写错误\n";
                
                // 找到具体行号
                $lines = explode("\n", $content);
                foreach ($lines as $lineNum => $line) {
                    if (strpos($line, 'sessioon_id') !== false) {
                        echo "     第" . ($lineNum + 1) . "行: " . trim($line) . "\n";
                    }
                }
            } else {
                echo "   ✅ 文件 {$file} 无拼写错误\n";
            }
        }
    }
    
    // 2. 检查是否有历史数据问题
    echo "\n🔍 检查历史数据:\n";
    
    // 连接数据库检查是否有历史的错误数据
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 检查数据库中是否有相关的错误记录
    $stmt = $pdo->query("SELECT session_id FROM p_websocket_sessions WHERE session_id LIKE '%sessioon%' LIMIT 5");
    $errorSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($errorSessions)) {
        echo "   ❌ 数据库中发现包含拼写错误的session_id:\n";
        foreach ($errorSessions as $session) {
            echo "     • {$session['session_id']}\n";
        }
    } else {
        echo "   ✅ 数据库中无拼写错误的session_id\n";
    }
    
    // 3. 分析可能的原因
    echo "\n🔧 可能的原因分析:\n";
    echo "   1. 历史代码中可能存在拼写错误，已经写入缓存\n";
    echo "   2. 内存表数据可能被其他进程或代码修改\n";
    echo "   3. 可能存在字符编码问题\n";
    echo "   4. 可能是序列化/反序列化过程中的问题\n";
    
    // 4. 建议的修复方案
    echo "\n💡 修复建议:\n";
    echo "   1. 清理所有现有的心跳缓存\n";
    echo "   2. 重启WebSocket服务器\n";
    echo "   3. 监控新的心跳缓存是否还有拼写错误\n";
    echo "   4. 如果问题持续，检查内存表的数据设置逻辑\n";
    
    $redis->close();
    
} catch (Exception $e) {
    echo "❌ 分析失败: " . $e->getMessage() . "\n";
}
