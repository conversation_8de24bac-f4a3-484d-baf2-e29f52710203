<?php
/**
 * WebSocket服务器监控脚本
 * 简单的状态检查，不依赖Laravel框架
 */

// 数据库连接配置
$host = '127.0.0.1';
$dbname = 'ai_tool';
$username = 'root';
$password = 'rootroot';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🚀 WebSocket服务器状态监控\n";
    echo "============================\n\n";
    
    // 1. 检查活跃连接数
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM p_websocket_sessions WHERE status = 'connected'");
    $activeConnections = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "📊 活跃连接数: {$activeConnections}\n";

    // 2. 检查总连接数
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM p_websocket_sessions");
    $totalConnections = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "📈 总连接数: {$totalConnections}\n";
    
    // 3. 检查最近1小时的连接
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM p_websocket_sessions WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $recentConnections = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "🕐 最近1小时连接: {$recentConnections}\n";

    // 4. 检查超时连接
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM p_websocket_sessions WHERE status = 'connected' AND last_ping_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)");
    $timeoutConnections = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "⏰ 超时连接数: {$timeoutConnections}\n";

    // 5. 检查断开连接数
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM p_websocket_sessions WHERE status = 'disconnected' AND disconnected_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $disconnectedConnections = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "🔌 最近1小时断开: {$disconnectedConnections}\n";
    
    // 6. 检查业务类型分布
    echo "\n📋 业务类型分布:\n";
    $stmt = $pdo->query("SELECT business_type, COUNT(*) as count FROM p_websocket_sessions WHERE status = 'connected' GROUP BY business_type");
    $businessTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($businessTypes)) {
        echo "   暂无活跃连接\n";
    } else {
        foreach ($businessTypes as $type) {
            echo "   • {$type['business_type']}: {$type['count']} 个连接\n";
        }
    }
    
    // 7. 检查最近的连接活动
    echo "\n🔄 最近连接活动:\n";
    $stmt = $pdo->query("SELECT session_id, user_id, status, connected_at, last_ping_at FROM p_websocket_sessions ORDER BY created_at DESC LIMIT 5");
    $recentSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($recentSessions as $session) {
        $sessionId = substr($session['session_id'], 0, 20) . '...';
        $status = $session['status'];
        $connectedAt = $session['connected_at'] ? date('H:i:s', strtotime($session['connected_at'])) : 'N/A';
        $lastPing = $session['last_ping_at'] ? date('H:i:s', strtotime($session['last_ping_at'])) : 'N/A';
        
        echo "   • {$sessionId} | 用户{$session['user_id']} | {$status} | 连接:{$connectedAt} | 心跳:{$lastPing}\n";
    }
    
    // 8. 健康状态评估
    echo "\n🏥 健康状态评估:\n";
    
    $maxConnections = 1000; // 从配置获取
    $connectionUsage = ($activeConnections / $maxConnections) * 100;
    
    echo "   • 连接使用率: " . round($connectionUsage, 2) . "%\n";
    
    if ($connectionUsage < 50) {
        echo "   • 状态: ✅ 良好\n";
    } elseif ($connectionUsage < 80) {
        echo "   • 状态: ⚠️ 注意\n";
    } else {
        echo "   • 状态: 🚨 警告 - 连接数过高\n";
    }
    
    if ($timeoutConnections > 0) {
        echo "   • 超时连接: ⚠️ 发现 {$timeoutConnections} 个超时连接\n";
    } else {
        echo "   • 超时连接: ✅ 无超时连接\n";
    }
    
    // 9. 修复效果验证
    echo "\n🔧 修复效果验证:\n";
    
    // 检查是否有重复的定时器执行（通过日志频率判断）
    $logFile = 'php/api/storage/logs/lumen-' . date('Y-m-d') . '.log';
    if (file_exists($logFile)) {
        $logContent = file_get_contents($logFile);
        $heartbeatCount = substr_count($logContent, 'WebSocket按session_id批量心跳更新成功');
        $cleanupCount = substr_count($logContent, 'WebSocket会话状态清理完成');
        
        echo "   • 心跳更新日志: {$heartbeatCount} 次\n";
        echo "   • 清理任务日志: {$cleanupCount} 次\n";
        
        if ($heartbeatCount > 0 && $cleanupCount >= 0) {
            echo "   • 定时器状态: ✅ 正常运行\n";
        } else {
            echo "   • 定时器状态: ⚠️ 可能异常\n";
        }
    }
    
    // 10. 建议
    echo "\n💡 建议:\n";
    if ($activeConnections == 0) {
        echo "   • 当前无活跃连接，可以测试新连接建立\n";
    }
    if ($timeoutConnections > 0) {
        echo "   • 建议检查客户端心跳机制\n";
    }
    if ($connectionUsage > 80) {
        echo "   • 建议增加服务器资源或优化连接管理\n";
    }
    
    echo "\n✅ 监控完成 - " . date('Y-m-d H:i:s') . "\n";
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ 监控失败: " . $e->getMessage() . "\n";
}
