<?php

namespace App\Services\PyApi;

use App\Models\Project;
use App\Models\ProjectStoryboard;
use App\Models\ProjectScenario;
use App\Models\ProjectCharacter;
use App\Models\StoryboardCharacter;
use App\Models\StyleLibrary;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;
use Carbon\Carbon;
use App\Services\Common\TransactionManager;

/**
 * 项目分镜业务服务层
 * 处理分镜相关的业务逻辑
 */
class ProjectStoryboardService
{
    /**
     * 检查项目访问权限
     *
     * @param int $userId 用户ID
     * @param int $projectId 项目ID
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    public function checkProjectAccess(int $userId, int $projectId): array
    {
        try {
            $project = Project::where('id', $projectId)
                ->where('user_id', $userId)
                ->first();

            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在或无权访问',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '权限验证通过',
                'data' => ['project' => $project]
            ];

        } catch (\Exception $e) {
            Log::error('项目权限检查失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'project_id' => $projectId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '权限检查失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取项目分镜列表
     *
     * @param int $projectId 项目ID
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    public function getProjectStoryboards(int $projectId, array $filters = [], int $page = 1, int $perPage = 10): array
    {
        try {
            $query = ProjectStoryboard::byProject($projectId)
                ->with(['characters'])
                ->orderByScene();

            // 应用筛选条件
            if (!empty($filters['status'])) {
                $query->byStatus($filters['status']);
            }

            // 分页查询
            $storyboards = $query->paginate($perPage, ['*'], 'page', $page);

            $data = [
                'storyboards' => $storyboards->items(),
                'pagination' => [
                    'current_page' => $storyboards->currentPage(),
                    'per_page' => $storyboards->perPage(),
                    'total' => $storyboards->total(),
                    'last_page' => $storyboards->lastPage(),
                    'has_more' => $storyboards->hasMorePages()
                ]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取分镜列表成功',
                'data' => $data
            ];

        } catch (\Exception $e) {
            Log::error('获取项目分镜列表失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取分镜列表失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取分镜详情
     *
     * @param int $storyboardId 分镜ID
     * @param int $userId 用户ID
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    public function getStoryboardDetail(int $storyboardId, int $userId): array
    {
        try {
            $storyboard = ProjectStoryboard::with(['project', 'characters.character', 'characters.projectCharacter'])
                ->find($storyboardId);

            if (!$storyboard) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分镜不存在',
                    'data' => []
                ];
            }

            // 检查权限
            if ($storyboard->project->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权访问该分镜',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取分镜详情成功',
                'data' => ['storyboard' => $storyboard]
            ];

        } catch (\Exception $e) {
            Log::error('获取分镜详情失败', [
                'method' => __METHOD__,
                'storyboard_id' => $storyboardId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取分镜详情失败',
                'data' => []
            ];
        }
    }

    /**
     * 创建分镜
     *
     * @param int $userId 用户ID
     * @param int $projectId 项目ID
     * @param array $storyboardData 分镜数据
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    public function createStoryboard(int $userId, int $projectId, array $storyboardData): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $projectCheck;
            }

            // 如果没有指定分镜序号，自动生成
            if (empty($storyboardData['scene_number'])) {
                $maxSceneNumber = ProjectStoryboard::byProject($projectId)->max('scene_number') ?? 0;
                $storyboardData['scene_number'] = $maxSceneNumber + 1;
            } else {
                // 检查序号是否已存在
                $existingStoryboard = ProjectStoryboard::byProject($projectId)
                    ->where('scene_number', $storyboardData['scene_number'])
                    ->first();

                if ($existingStoryboard) {
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => '分镜序号已存在',
                        'data' => []
                    ];
                }
            }

            // 创建分镜
            $storyboard = ProjectStoryboard::create([
                'project_id' => $projectId,
                'scenarios_id' => $storyboardData['scenarios_id'],
                'scene_number' => $storyboardData['scene_number'],
                'scene_title' => $storyboardData['scene_title'],
                'subtitle' => $storyboardData['subtitle'],
                'status' => ProjectStoryboard::STATUS_DRAFT
            ]);

            DB::commit();

            Log::info('分镜创建成功', [
                'storyboard_id' => $storyboard->id,
                'project_id' => $projectId,
                'user_id' => $userId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '分镜创建成功',
                'data' => ['storyboard' => $storyboard]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('创建分镜失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'project_id' => $projectId,
                'storyboard_data' => LogCheckHelper::sanitize_request_for_log($storyboardData),
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '创建分镜失败',
                'data' => []
            ];
        }
    }

    /**
     * 更新分镜
     *
     * @param int $storyboardId 分镜ID
     * @param int $userId 用户ID
     * @param array $updateData 更新数据
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    public function updateStoryboard(int $storyboardId, int $userId, array $updateData): array
    {
        try {
            TransactionManager::begin('ProjectStoryboardService.updateStoryboard');

            $storyboard = ProjectStoryboard::with('project')->find($storyboardId);

            if (!$storyboard) {
                TransactionManager::rollback('ProjectStoryboardService.updateStoryboard', '分镜不存在');
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分镜不存在',
                    'data' => []
                ];
            }

            // 检查权限
            if ($storyboard->project->user_id !== $userId) {
                TransactionManager::rollback('ProjectStoryboardService.updateStoryboard', '无权修改该分镜');
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权修改该分镜',
                    'data' => []
                ];
            }

            // 如果更新分镜序号，检查是否冲突
            if (isset($updateData['scene_number']) && $updateData['scene_number'] !== $storyboard->scene_number) {
                $existingStoryboard = ProjectStoryboard::byProject($storyboard->project_id)
                    ->where('scene_number', $updateData['scene_number'])
                    ->where('id', '!=', $storyboardId)
                    ->first();

                if ($existingStoryboard) {
                    TransactionManager::rollback('ProjectStoryboardService.updateStoryboard', '分镜序号已存在');
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => '分镜序号已存在',
                        'data' => []
                    ];
                }
            }

            // 更新分镜
            $storyboard->update($updateData);

            TransactionManager::commit('ProjectStoryboardService.updateStoryboard');

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '分镜更新成功',
                'data' => ['storyboard' => $storyboard->fresh()]
            ];

        } catch (\Exception $e) {
            TransactionManager::rollback('ProjectStoryboardService.updateStoryboard', $e->getMessage());

            Log::error('更新分镜失败', [
                'method' => __METHOD__,
                'storyboard_id' => $storyboardId,
                'user_id' => $userId,
                'update_data' => LogCheckHelper::sanitize_request_for_log($updateData),
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '更新分镜失败',
                'data' => []
            ];
        }
    }

    /**
     * 删除分镜
     *
     * @param int $storyboardId 分镜ID
     * @param int $userId 用户ID
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    public function deleteStoryboard(int $storyboardId, int $userId): array
    {
        try {
            DB::beginTransaction();

            $storyboard = ProjectStoryboard::with('project')->find($storyboardId);

            if (!$storyboard) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分镜不存在',
                    'data' => []
                ];
            }

            // 检查权限
            if ($storyboard->project->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权删除该分镜',
                    'data' => []
                ];
            }

            // 删除分镜（会级联删除相关的角色关联）
            $storyboard->delete();

            DB::commit();

            Log::info('分镜删除成功', [
                'storyboard_id' => $storyboardId,
                'user_id' => $userId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '分镜删除成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('删除分镜失败', [
                'method' => __METHOD__,
                'storyboard_id' => $storyboardId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '删除分镜失败',
                'data' => []
            ];
        }
    }

    /**
     * 批量更新分镜排序
     *
     * @param int $userId 用户ID
     * @param int $projectId 项目ID
     * @param array $storyboards 分镜排序数据
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    public function reorderStoryboards(int $userId, int $projectId, array $storyboards): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $projectCheck;
            }

            // 验证所有分镜都属于该项目
            $storyboardIds = array_column($storyboards, 'id');
            $existingStoryboards = ProjectStoryboard::byProject($projectId)
                ->whereIn('id', $storyboardIds)
                ->get();

            if ($existingStoryboards->count() !== count($storyboardIds)) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '部分分镜不存在或不属于该项目',
                    'data' => []
                ];
            }

            // 批量更新排序
            foreach ($storyboards as $storyboardData) {
                ProjectStoryboard::where('id', $storyboardData['id'])
                    ->update(['scene_number' => $storyboardData['scene_number']]);
            }

            DB::commit();

            Log::info('分镜排序更新成功', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'storyboard_count' => count($storyboards)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '分镜排序更新成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('分镜排序失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'project_id' => $projectId,
                'storyboards' => LogCheckHelper::sanitize_request_for_log($storyboards),
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '分镜排序失败',
                'data' => []
            ];
        }
    }

    /**
     * 批量生成分镜
     *
     * @param int $userId 用户ID
     * @param int $projectId 项目ID
     * @param array $storyboardIds 分镜ID列表
     * @param array $generationParams 生成参数
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    public function batchGenerateStoryboards(int $userId, int $projectId, array $storyboardIds = [], array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $projectCheck;
            }

            // 获取要生成的分镜
            $query = ProjectStoryboard::byProject($projectId);

            if (!empty($storyboardIds)) {
                $query->whereIn('id', $storyboardIds);
            } else {
                // 如果没有指定分镜ID，则生成所有未生成的分镜
                $query->whereIn('status', [ProjectStoryboard::STATUS_DRAFT, ProjectStoryboard::STATUS_APPROVED]);
            }

            $storyboards = $query->get();

            if ($storyboards->isEmpty()) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有找到可生成的分镜',
                    'data' => []
                ];
            }

            $generatedCount = 0;
            $failedCount = 0;

            foreach ($storyboards as $storyboard) {
                try {
                    // 标记为生成中
                    $storyboard->markAsGenerating();

                    // 调用AI服务生成分镜图片
                    $this->generateStoryboardImage($storyboard, $generationParams, $userId);

                    $generatedCount++;
                } catch (\Exception $e) {
                    Log::error('单个分镜生成失败', [
                        'storyboard_id' => $storyboard->id,
                        'error' => $e->getMessage()
                    ]);

                    $storyboard->markAsFailed();
                    $failedCount++;
                }
            }

            DB::commit();

            Log::info('批量生成分镜完成', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'generated_count' => $generatedCount,
                'failed_count' => $failedCount
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => "批量生成完成，成功：{$generatedCount}个，失败：{$failedCount}个",
                'data' => [
                    'generated_count' => $generatedCount,
                    'failed_count' => $failedCount,
                    'total_count' => $storyboards->count()
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('批量生成分镜失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'project_id' => $projectId,
                'storyboard_ids' => $storyboardIds,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量生成分镜失败',
                'data' => []
            ];
        }
    }

    /**
     * 从故事内容拆解分镜（增强版：支持场景创建和项目更新）
     *
     * @param int $userId 用户ID
     * @param int $projectId 项目ID
     * @param int $styleId 风格ID
     * @param int $aspectRatio 尺寸ID
     * @param string $storyContent AI返回的结构化JSON格式分镜剧本
     * @return array
     */
    public function extractStoryboardsFromStory(int $userId, int $projectId, int $styleId, string $aspectRatio, string $storyContent): array
    {
        try {
            // TransactionManager 会自动处理嵌套事务，无需手动判断
            TransactionManager::begin('ProjectStoryboardService.extractStoryboardsFromStory');

            // 提取尺寸及风格信息（从用户提交的prompt数据中提取）
            $dimensionInfo = $this->extractDimensionInfoFromAiResponse($styleId, $aspectRatio);

            // 直接解析AI返回的结构化JSON格式分镜剧本（不再调用AI）
            $parseResult = $this->parseAiStoryboardJson($storyContent);
            if ($parseResult['code'] !== ApiCodeEnum::SUCCESS) {
                // 解析失败，回滚事务并返回错误
                TransactionManager::rollback('ProjectStoryboardService.extractStoryboardsFromStory', $parseResult['message']);
                return $parseResult;
            }
            $analysisResult = $parseResult['data'];

            // 更新项目配置
            $analysisResult['project_info']['project_config'] = $dimensionInfo;

            // 更新项目状态为进行中
            $analysisResult['project_info']['status'] = 'in_progress';

            // 更新项目信息
            $projectService = new \App\Services\PyApi\ProjectService();
            $projectResult = $projectService->updateProject($projectId, $userId, $analysisResult['project_info']);

            // 检查项目更新是否成功
            if ($projectResult['code'] !== ApiCodeEnum::SUCCESS) {
                // 回滚事务并返回错误
                TransactionManager::rollback('ProjectStoryboardService.extractStoryboardsFromStory', $projectResult['message']);
                return [
                    'code' => $projectResult['code'],
                    'message' => '项目信息更新失败: ' . $projectResult['message'],
                    'data' => $projectResult['data']
                ];
            }

            // 创建场景
            try {
                $createdScenarios = $this->batchCreateScenarios($projectId, $analysisResult['scenarios']);
            } catch (\Exception $e) {
                TransactionManager::rollback('ProjectStoryboardService.extractStoryboardsFromStory', $e->getMessage());
                return [
                    'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                    'message' => '场景创建失败: ' . $e->getMessage(),
                    'data' => []
                ];
            }

            // 创建分镜（关联场景）
            try {
                $createdStoryboards = $this->batchCreateStoryboards($projectId, $analysisResult['storyboards'], $createdScenarios);
            } catch (\Exception $e) {
                TransactionManager::rollback('ProjectStoryboardService.extractStoryboardsFromStory', $e->getMessage());
                return [
                    'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                    'message' => '分镜创建失败: ' . $e->getMessage(),
                    'data' => []
                ];
            }

            // 创建分镜角色关联
            try {
                $this->batchCreateStoryboardCharacters($createdStoryboards, $analysisResult['storyboards']);
            } catch (\Exception $e) {
                TransactionManager::rollback('ProjectStoryboardService.extractStoryboardsFromStory', $e->getMessage());
                return [
                    'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                    'message' => '分镜角色关联创建失败: ' . $e->getMessage(),
                    'data' => []
                ];
            }

            // 创建项目角色记录（基于提取的角色信息）
            try {
                $createdCharacters = $this->batchCreateProjectCharacters($projectId, $analysisResult['characters'] ?? []);
            } catch (\Exception $e) {
                TransactionManager::rollback('ProjectStoryboardService.extractStoryboardsFromStory', $e->getMessage());
                return [
                    'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                    'message' => '项目角色创建失败: ' . $e->getMessage(),
                    'data' => []
                ];
            }

            // TransactionManager 会自动处理嵌套事务的提交
            TransactionManager::commit('ProjectStoryboardService.extractStoryboardsFromStory');

            Log::info('故事分镜拆解成功', [
                'project_id' => $projectId,
                'scenario_count' => count($createdScenarios),
                'storyboard_count' => count($createdStoryboards),
                'character_count' => count($createdCharacters)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '故事分镜拆解成功',
                'data' => [
                    'project_id' => $projectId,
                    'project' => $projectResult['data'],
                    'scenarios' => $createdScenarios,
                    'storyboards' => $createdStoryboards,
                    'characters' => $createdCharacters,
                    'scenario_count' => count($createdScenarios),
                    'storyboard_count' => count($createdStoryboards),
                    'character_count' => count($createdCharacters)
                ]
            ];

        } catch (\Exception $e) {
            // TransactionManager 会自动处理嵌套事务的回滚
            TransactionManager::rollback('ProjectStoryboardService.extractStoryboardsFromStory', $e->getMessage());

            Log::error('故事分镜拆解失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '故事分镜拆解失败',
                'data' => []
            ];
        }
    }

    /**
     * 解析AI返回的结构化JSON格式分镜剧本
     *
     * 传入的 $storyContent 应该是AI返回的结构化JSON格式，包含：
     * - 故事标题
     * - 故事简概
     * - 场景1, 场景2, 场景3...
     * 每个场景包含：场景名称、空间、时间、天气、场景提示词、分镜数组
     * 每个分镜包含：分镜序号、出境角色、字幕、分镜提示词
     *
     * @param string $storyContent AI返回的JSON格式分镜剧本
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    private function parseAiStoryboardJson(string $storyContent): array
    {
        try {
            // 解析JSON格式的分镜剧本
            $parsedData = json_decode($storyContent, true);

            if (!is_array($parsedData))
            {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '分镜剧本JSON格式无效',
                    'data' => []
                ];
            }

            // 验证数据完整性
            $validationResult = $this->validateAiStoryboardData($parsedData);
            if ($validationResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $validationResult;
            }


            // 提取项目更新需要的信息(提取项目标题及简介信息)
            $projectInfo = $this->extractProjectInfoFromAiResponse($parsedData);

            // 提取场景信息
            $scenarios = $this->extractScenesFromAiResponse($parsedData);

            // 提取分镜信息
            $storyboards = $this->extractStoryboardsFromAiResponse($parsedData, $scenarios);

            // 提取角色信息
            $characters = $this->extractCharactersFromAiResponse($parsedData);


            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'AI分镜剧本解析成功',
                'data' => [
                    'project_info' => $projectInfo,
                    'scenarios' => $scenarios,
                    'storyboards' => $storyboards,
                    'characters' => $characters
                ]
            ];

        } catch (\Exception $e) {
            Log::error('AI分镜剧本解析失败', [
                'error' => $e->getMessage(),
                'story_content' => substr($storyContent, 0, 200) . '...'
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => 'AI分镜剧本解析失败: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 验证自有返回的分镜剧本数据完整性
     * 适用于新的JSON格式：{"故事标题": "...", "故事简概": "...", "分镜1": {
        "场景名称": "咖啡厅包间",
        "空间": "室内",
        "时间": "白天",
        "天气": "晴",
        "出境角色": "陆星,管家",
        "字幕": "“这里有一百万，离开青鱼小姐。”“我们的交易，到此结束。”咚！一个旅行袋被随意丢在桌子上。",
        "分镜提示词": "一位西装革履的管家将一个黑色旅行袋丢在桌子上，对面的少年陆星面无表情地看着他，光线从窗户照入，氛围紧张。"
     * }, 
     * "分镜2": {
        "场景名称": "咖啡厅包间",
        "空间": "室内",
        "时间": "白天",
        "天气": "晴",
        "出境角色": "陆星",
        "字幕": "陆星拉开背包，一叠叠红色钞票争先恐后掉在他身上！这个世界上最伟大的颜色——红色！",
        "分镜提示词": "特写镜头，少年陆星拉开旅行袋拉链，无数叠崭新的人民币百元大钞从包里涌出，散落在桌上和他的身上，他眼神发光。"
     * }
     * "分镜N": {
        "场景名称": "学校",
        "空间": "室外",
        "时间": "两年半之前",
        "天气": "晴",
        "出境角色": "魏青鱼",
        "字幕": "（回忆）两年半之前，魏青鱼进入学校。她长得清冷精致，但性格冷漠寡言。大把的人为她的美貌前仆后继，但都倒在她的冷眼之下。得不到就毁掉的态度，一时之间，谣言四起。",
        "分镜提示词": "【回忆风格】美丽的少女魏青鱼在校园里独自行走，表情冷漠，周围的同学或爱慕或嫉妒地看着她，背景中浮现出一些文字气泡代表谣言。"
     * }
     * }
     *
     * @param array $data AI返回的分镜剧本数据
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    private function validateMyStoryboardData(array $data): array
    {
        // 检查基本结构
        if (!isset($data['故事标题']) || empty($data['故事标题'])) {
            return [
                'code' => ApiCodeEnum::VALIDATION_ERROR,
                'message' => '缺少故事标题',
                'data' => []
            ];
        }

        if (!isset($data['故事简概']) || empty($data['故事简概'])) {
            return [
                'code' => ApiCodeEnum::VALIDATION_ERROR,
                'message' => '缺少故事简概',
                'data' => []
            ];
        }

        // 查找分镜数据
        $storyboardCount = 0;
        foreach ($data as $key => $value) {
            if (preg_match('/^分镜\d+$/', $key) && is_array($value)) {
                $storyboardCount++;

                // 验证分镜必需字段
                $requiredStoryboardFields = ['场景名称', '空间', '时间', '天气', '出境角色', '字幕', '分镜提示词'];
                foreach ($requiredStoryboardFields as $field) {
                    if (!isset($value[$field]) || empty($value[$field])) {
                        return [
                            'code' => ApiCodeEnum::VALIDATION_ERROR,
                            'message' => "分镜 > {$key} > 缺少必需字段: {$field}",
                            'data' => []
                        ];
                    }
                }

                // 验证字段类型和格式
                if (!is_string($value['场景名称']) || !is_string($value['空间']) ||
                    !is_string($value['时间']) || !is_string($value['天气']) ||
                    !is_string($value['出境角色']) || !is_string($value['字幕']) ||
                    !is_string($value['分镜提示词'])) {
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => "分镜 > {$key} > 字段类型必须为字符串",
                        'data' => []
                    ];
                }
            }
        }

        if ($storyboardCount === 0) {
            return [
                'code' => ApiCodeEnum::VALIDATION_ERROR,
                'message' => '至少需要一个分镜',
                'data' => []
            ];
        }

        // 验证通过
        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => '数据验证通过',
            'data' => []
        ];
    }
    
    /**
     * 验证AI返回的分镜剧本数据完整性
     * 适用于新的JSON格式：{"故事标题": "...", "故事简概": "...", "场景1": {...}, "场景2": {...}}
     *
     * @param array $data AI返回的分镜剧本数据
     * @return array 返回数据格式：['code' => int, 'message' => string, 'data' => array]
     */
    private function validateAiStoryboardData(array $data): array
    {
        // 检查基本结构
        if (!isset($data['故事标题']) || empty($data['故事标题'])) {
            return [
                'code' => ApiCodeEnum::VALIDATION_ERROR,
                'message' => '缺少故事标题',
                'data' => []
            ];
        }

        if (!isset($data['故事简概']) || empty($data['故事简概'])) {
            return [
                'code' => ApiCodeEnum::VALIDATION_ERROR,
                'message' => '缺少故事简概',
                'data' => []
            ];
        }

        // 查找场景数据
        $sceneCount = 0;
        foreach ($data as $key => $value) {
            if (preg_match('/^场景\d+$/', $key) && is_array($value)) {
                $sceneCount++;

                // 验证场景必需字段
                $requiredSceneFields = ['场景名称', '空间', '时间', '天气', '场景提示词', '分镜'];
                foreach ($requiredSceneFields as $field) {
                    if (!isset($value[$field]) || ($field !== '分镜' && empty($value[$field]))) {
                        return [
                            'code' => ApiCodeEnum::VALIDATION_ERROR,
                            'message' => "场景 > {$key} > 缺少必需字段: {$field}",
                            'data' => []
                        ];
                    }
                }

                // 验证分镜数组
                if (!is_array($value['分镜']) || empty($value['分镜'])) {
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => "场景 > {$key} > 的分镜数组为空",
                        'data' => []
                    ];
                }

                // 验证每个分镜的必需字段
                foreach ($value['分镜'] as $index => $storyboard) {
                    $requiredStoryboardFields = ['分镜序号', '出境角色', '字幕', '分镜提示词'];
                    foreach ($requiredStoryboardFields as $field) {
                        if (!isset($storyboard[$field]) || empty($storyboard[$field])) {
                            return [
                                'code' => ApiCodeEnum::VALIDATION_ERROR,
                                'message' => "场景 > {$key} > 分镜".($index+1)." 缺少必需字段: {$field}",
                                'data' => []
                            ];
                        }
                    }
                }
            }
        }

        if ($sceneCount === 0) {
            return [
                'code' => ApiCodeEnum::VALIDATION_ERROR,
                'message' => '至少需要一个场景',
                'data' => []
            ];
        }

        // 验证通过
        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => '数据验证通过',
            'data' => []
        ];
    }

    /**
     * 从AI返回的JSON中提取项目创建信息
     * 提取故事标题、故事简概等项目基本信息
     */
    private function extractProjectInfoFromAiResponse(array $data): array
    {
        return [
            'title' => $data['故事标题'] ?? '',
            'description' => $data['故事简概'] ?? '',
            'story_content' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'title_confirmed' => true, // 从AI分镜剧本创建的项目，标题已确认
        ];
    }

    /**
     * 从用户提交的prompt数据中提取尺寸和风格信息
     * @param int $styleId 风格库ID
     * @param string $aspectRatio 尺寸比例
     */
    private function extractDimensionInfoFromAiResponse(int $styleId, string $aspectRatio): array
    {
        $dimensionInfo = [
            'video_width' => 1920,      // 默认宽度
            'video_height' => 1080,     // 默认高度
            'aspect_ratio' => '16:9',   // 默认宽高比
            'resolution' => '1080p',    // 默认分辨率
            'frame_rate' => 30,         // 默认帧率
            'style_id' => $styleId,     // 风格库ID
            'style_name' => '',         // 风格名称
            'style_params' => [],       // 风格参数
            'ai_prompt' => '',          // 风格提示词模板
        ];

        // 1. 处理尺寸信息
        $dimensionInfo['aspect_ratio'] = $aspectRatio;

        // 根据比例计算具体的宽高
        switch ($aspectRatio) {
            case '16:9':
                $dimensionInfo['video_width'] = 1920;
                $dimensionInfo['video_height'] = 1080;
                $dimensionInfo['resolution'] = '1080p';
                break;
            case '4:3':
                $dimensionInfo['video_width'] = 1024;
                $dimensionInfo['video_height'] = 768;
                $dimensionInfo['resolution'] = '768p';
                break;
            case '9:16':
                $dimensionInfo['video_width'] = 1080;
                $dimensionInfo['video_height'] = 1920;
                $dimensionInfo['resolution'] = '1080p';
                break;
            case '3:4':
                $dimensionInfo['video_width'] = 768;
                $dimensionInfo['video_height'] = 1024;
                $dimensionInfo['resolution'] = '768p';
                break;
            default:
                // 保持默认值
                break;
        }

        return $dimensionInfo;
    }

    /**
     * 从AI返回的JSON中提取场景信息
     * 适用于新的JSON格式：{"场景1": {...}, "场景2": {...}}
     */
    private function extractScenesFromAiResponse(array $data): array
    {
        $scenarios = [];
        $sceneOrder = 1;

        foreach ($data as $key => $value) {
            if (preg_match('/^场景(\d+)$/', $key, $matches) && is_array($value)) {
                $scenarios[] = [
                    'scene_name' => $value['场景名称'],
                    'space' => $value['空间'],
                    'time' => $value['时间'],
                    'weather' => $value['天气'],
                    'scene_prompt' => $value['场景提示词'],
                    'scene_order' => $sceneOrder++,
                    'metadata' => [
                        'original_key' => $key,
                        'scene_number' => intval($matches[1])
                    ]
                ];
            }
        }

        return $scenarios;
    }

    /**
     * 从AI返回的JSON中提取分镜信息
     * 适用于新的JSON格式，每个场景包含分镜数组
     */
    private function extractStoryboardsFromAiResponse(array $data, array $scenarios): array
    {
        $storyboards = [];
        $globalStoryboardNumber = 1;

        foreach ($data as $key => $value) {
            if (preg_match('/^场景(\d+)$/', $key, $matches) && is_array($value)) {
                $sceneNumber = intval($matches[1]);

                // 找到对应的场景索引
                $scenarioIndex = $sceneNumber - 1;

                if (isset($value['分镜']) && is_array($value['分镜'])) {
                    foreach ($value['分镜'] as $storyboardData) {
                        $storyboards[] = [
                            'scenarios_id' => $sceneNumber, // 添加场景ID字段，用于关联场景
                            'scene_number' => $globalStoryboardNumber++,
                            'scene_title' => $value['场景名称'] . ' - 分镜' . $storyboardData['分镜序号'],
                            'subtitle' => $storyboardData['字幕'],
                            'ai_prompt' => $storyboardData['分镜提示词'],
                            'generation_params' => [
                                'characters' => $storyboardData['出境角色'],
                                'scene_context' => $value['场景名称'],
                                'scene_prompt' => $value['场景提示词'],
                                'storyboard_number' => $storyboardData['分镜序号']
                            ],
                            'metadata' => [
                                'original_scene_key' => $key,
                                'original_scene_number' => $sceneNumber,
                                'original_storyboard_number' => $storyboardData['分镜序号'],
                                'scenario_index' => $scenarioIndex
                            ]
                        ];
                    }
                }
            }
        }

        return $storyboards;
    }

    /**
     * 批量创建场景（支持更新已存在的场景）
     */
    private function batchCreateScenarios(int $projectId, array $scenariosData): array
    {
        $createdScenarios = [];

        foreach ($scenariosData as $scenarioData) {
            // 使用 updateOrCreate 避免唯一约束冲突
            // 如果存在相同的 project_id 和 scene_order，则更新；否则创建新记录
            $scenario = ProjectScenario::updateOrCreate(
                [
                    'project_id' => $projectId,
                    'scene_order' => $scenarioData['scene_order']
                ],
                [
                    'scene_name' => $scenarioData['scene_name'],
                    'space' => $scenarioData['space'],
                    'time' => $scenarioData['time'],
                    'weather' => $scenarioData['weather'],
                    'scene_prompt' => $scenarioData['scene_prompt'],
                    'metadata' => $scenarioData['metadata']
                ]
            );

            $createdScenarios[] = $scenario;
        }

        return $createdScenarios;
    }

    /**
     * 批量创建分镜记录
     */
    private function batchCreateStoryboards(int $projectId, array $storyboardsData, array $scenarios): array
    {
        $createdStoryboards = [];

        foreach ($storyboardsData as $storyboardData) {
            // 根据scenarios_id找到对应的场景
            $scenarioId = $storyboardData['scenarios_id'];
            $scenario = null;
            foreach ($scenarios as $s) {
                if ($s->scene_order == $scenarioId) {
                    $scenario = $s;
                    break;
                }
            }

            if (!$scenario) {
                // 如果找不到对应场景，使用第一个场景
                $scenario = $scenarios[0] ?? null;
            }

            if ($scenario) {
                // 提取角色信息
                $characters = $storyboardData['generation_params']['characters'] ?? '';
                $characterNames = $this->parseCharacterNames($characters);

                // 使用 updateOrCreate 避免唯一约束冲突
                // 如果存在相同的 project_id 和 scene_number，则更新；否则创建新记录
                $storyboard = ProjectStoryboard::updateOrCreate(
                    [
                        'project_id' => $projectId,
                        'scene_number' => $storyboardData['scene_number']
                    ],
                    [
                        'scenarios_id' => $scenario->id,
                        'scene_title' => $storyboardData['scene_title'],
                        'subtitle' => $storyboardData['subtitle'],
                        'ai_prompt' => $storyboardData['ai_prompt'],
                        'generation_params' => $storyboardData['generation_params'],
                        'character_ids' => $characterNames, // 存储角色名称数组
                        'metadata' => $storyboardData['metadata'],
                        'status' => ProjectStoryboard::STATUS_DRAFT
                    ]
                );

                $createdStoryboards[] = $storyboard;
            }
        }

        return $createdStoryboards;
    }

    /**
     * 批量创建或更新分镜角色关联
     * 判断 storyboard_id + character_name 组合是否存在，存在则更新，不存在则创建
     */
    private function batchCreateStoryboardCharacters(array $createdStoryboards, array $storyboardsData): void
    {
        foreach ($createdStoryboards as $index => $storyboard) {
            // 获取对应的分镜数据
            $storyboardData = $storyboardsData[$index] ?? null;
            if (!$storyboardData) {
                continue;
            }

            // 从generation_params中提取角色信息
            $characters = $storyboardData['generation_params']['characters'] ?? '';

            if (empty($characters)) {
                continue;
            }

            // 解析角色字符串，可能是逗号分隔的角色名称
            $characterNames = $this->parseCharacterNames($characters);

            // 为每个角色创建或更新关联记录
            foreach ($characterNames as $characterName) {
                if (!empty(trim($characterName))) {
                    // 使用 updateOrCreate 实现存在则更新，不存在则创建的逻辑
                    StoryboardCharacter::updateOrCreate(
                        [
                            // 查找条件：只用 storyboard_id
                            'storyboard_id' => $storyboard->id
                        ],
                        [
                            // 更新或创建的数据 - 包含所有字段确保完整更新
                            'character_name' => trim($characterName), // character_name 作为需要更新的字段
                            'character_id' => null, // 重置角色库绑定
                            'project_character_id' => null, // 重置项目角色绑定
                            'character_description' => $storyboardData['subtitle'] ?? null,
                            'position_description' => $storyboardData['generation_params']['scene_context'] ?? null,
                            'action_description' => $storyboardData['ai_prompt'] ?? null,
                            'binding_status' => StoryboardCharacter::STATUS_UNBOUND
                        ]
                    );
                }
            }
        }
    }

    /**
     * 解析角色名称字符串
     * 支持多种格式：逗号分隔、顿号分隔、空格分隔等
     */
    private function parseCharacterNames(string $charactersString): array
    {
        // 移除常见的前缀词
        $charactersString = preg_replace('/^(出境角色[:：]?|角色[:：]?|人物[:：]?)\s*/u', '', $charactersString);

        // 使用多种分隔符进行分割，添加u修饰符支持UTF-8
        $characters = preg_split('/[,，、]\s*/u', $charactersString, -1, PREG_SPLIT_NO_EMPTY);

        // 如果没有找到分隔符，尝试按空格分割
        if (count($characters) === 1) {
            $characters = preg_split('/\s+/u', $charactersString, -1, PREG_SPLIT_NO_EMPTY);
        }

        // 清理和过滤角色名称
        $cleanedCharacters = [];
        foreach ($characters as $character) {
            $character = trim($character);
            // 过滤掉一些无效的角色名称
            if (!empty($character) && !in_array($character, ['无', '无人', '旁白', '解说'])) {
                $cleanedCharacters[] = $character;
            }
        }

        return $cleanedCharacters;
    }

    /**
     * 生成分镜图片
     *
     * 🚨 重构说明：
     * ✅ 调用 ImageService 进行图片生成，符合职责分离原则
     * ✅ ImageController 负责AI调用、积分处理、状态管理
     * ✅ ProjectStoryboardService 负责分镜业务逻辑
     */
    private function generateStoryboardImage(ProjectStoryboard $storyboard, array $generationParams, int $userId): void
    {
        try {
            // 获取分镜的提示词
            $prompt = $storyboard->ai_prompt ?: $storyboard->scene_description;

            // 构建图片生成参数
            $imageGenerationParams = [
                'style' => $generationParams['style'] ?? null,
                'aspect_ratio' => $generationParams['aspect_ratio'] ?? '16:9',
                'quality' => $generationParams['quality'] ?? 'standard',
                'platform' => $generationParams['platform'] ?? 'liblib'
            ];

            // 调用 ImageService 进行图片生成（这是正确的架构）
            $imageService = app(\App\Services\PyApi\ImageService::class);
            $result = $imageService->generateImage(
                $userId,
                $prompt,
                null, // character_id
                $storyboard->project_id,
                $imageGenerationParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 图片生成成功，标记为完成
                $storyboard->markAsCompleted();

                // 保存生成的资源ID（如果有）
                if (isset($result['data']['resource_id']) && $result['data']['resource_id']) {
                    $storyboard->generated_image_id = $result['data']['resource_id'];
                }

                // 保存生成信息到元数据中
                $metadata = $storyboard->metadata ?? [];
                $metadata['image_generation_result'] = [
                    'image_url' => $result['data']['image_url'] ?? '',
                    'thumbnail_url' => $result['data']['thumbnail_url'] ?? '',
                    'cost' => $result['data']['cost'] ?? 0,
                    'platform' => $result['data']['platform'] ?? '',
                    'generated_at' => Carbon::now('Asia/Shanghai')->format('c')
                ];
                $storyboard->metadata = $metadata;
                $storyboard->save();
            } else {
                // 图片生成失败
                $storyboard->markAsFailed();
                throw new \Exception('图片生成失败：' . ($result['message'] ?? '未知错误'));
            }

        } catch (\Exception $e) {
            $storyboard->markAsFailed();
            throw $e;
        }
    }

    /**
     * 选择性生成分镜图片
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient::callWithUserChoice 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     */
    public function selectiveGenerateStoryboards(int $userId, int $projectId, array $filters = [], array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $projectCheck;
            }

            // 构建查询条件
            $query = ProjectStoryboard::byProject($projectId);

            // 应用筛选条件
            if (!empty($filters['status'])) {
                $query->whereIn('status', $filters['status']);
            } else {
                // 默认只处理草稿和已批准的分镜
                $query->whereIn('status', [ProjectStoryboard::STATUS_DRAFT, ProjectStoryboard::STATUS_APPROVED]);
            }

            if (isset($filters['has_ai_prompt']) && $filters['has_ai_prompt']) {
                $query->whereNotNull('ai_prompt')->where('ai_prompt', '!=', '');
            }

            if (!empty($filters['scene_types'])) {
                // 这里可以根据场景类型进行筛选，需要根据实际的场景类型字段调整
                // 假设有scene_type字段或者通过scene_description进行模糊匹配
                $query->where(function($q) use ($filters) {
                    foreach ($filters['scene_types'] as $type) {
                        $q->orWhere('scene_description', 'like', "%{$type}%");
                    }
                });
            }

            $storyboards = $query->get();

            if ($storyboards->isEmpty()) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有找到符合筛选条件的分镜',
                    'data' => []
                ];
            }

            $generatedCount = 0;
            $failedCount = 0;
            $skippedCount = 0;

            foreach ($storyboards as $storyboard) {
                try {
                    // 检查是否正在生成中
                    if ($storyboard->status === ProjectStoryboard::STATUS_GENERATING) {
                        $skippedCount++;
                        continue;
                    }

                    // 标记为生成中
                    $storyboard->markAsGenerating();

                    // 调用AI服务生成分镜图片
                    $this->generateStoryboardImage($storyboard, $generationParams, $userId);

                    $generatedCount++;
                } catch (\Exception $e) {
                    Log::error('选择性分镜生成失败', [
                        'storyboard_id' => $storyboard->id,
                        'error' => $e->getMessage()
                    ]);

                    $storyboard->markAsFailed();
                    $failedCount++;
                }
            }

            DB::commit();

            Log::info('选择性分镜图片生成完成', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'filters' => $filters,
                'total_matched' => $storyboards->count(),
                'generated_count' => $generatedCount,
                'failed_count' => $failedCount,
                'skipped_count' => $skippedCount
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '选择性分镜图片生成完成',
                'data' => [
                    'project_id' => $projectId,
                    'filters_applied' => $filters,
                    'summary' => [
                        'total_matched' => $storyboards->count(),
                        'generated_count' => $generatedCount,
                        'failed_count' => $failedCount,
                        'skipped_count' => $skippedCount
                    ],
                    'generation_params' => $generationParams
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('选择性分镜图片生成失败', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'filters' => $filters,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '选择性分镜图片生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取选择性分镜ID列表
     */
    public function getSelectiveStoryboardIds(int $userId, int $projectId, array $filters): array
    {
        try {
            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return [];
            }

            $query = ProjectStoryboard::byProject($projectId);

            // 应用筛选条件
            if (!empty($filters['status'])) {
                $query->whereIn('status', $filters['status']);
            }

            if (isset($filters['has_ai_prompt']) && $filters['has_ai_prompt']) {
                $query->where(function($q) {
                    $q->whereNotNull('ai_prompt')
                      ->where('ai_prompt', '!=', '');
                });
            }

            if (!empty($filters['scene_types'])) {
                $query->whereIn('scene_type', $filters['scene_types']);
            }

            return $query->pluck('id')->toArray();

        } catch (\Exception $e) {
            Log::error('获取选择性分镜ID失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取项目所有分镜ID列表
     */
    public function getAllProjectStoryboardIds(int $userId, int $projectId): array
    {
        try {
            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return [];
            }

            return ProjectStoryboard::byProject($projectId)
                ->pluck('id')
                ->toArray();

        } catch (\Exception $e) {
            Log::error('获取项目分镜ID失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 验证分镜ID列表
     */
    public function validateStoryboardIds(int $userId, int $projectId, array $storyboardIds): array
    {
        try {
            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return [];
            }

            return ProjectStoryboard::byProject($projectId)
                ->whereIn('id', $storyboardIds)
                ->pluck('id')
                ->toArray();

        } catch (\Exception $e) {
            Log::error('验证分镜ID失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'storyboard_ids' => $storyboardIds,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取未处理的分镜ID列表
     */
    public function getUnprocessedStoryboardIds(int $userId, int $projectId): array
    {
        try {
            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return [];
            }

            return ProjectStoryboard::byProject($projectId)
                ->where(function($query) {
                    $query->whereNull('generated_image_id')
                          ->orWhere('status', ProjectStoryboard::STATUS_FAILED);
                })
                ->where('status', '!=', ProjectStoryboard::STATUS_GENERATING)
                ->pluck('id')
                ->toArray();

        } catch (\Exception $e) {
            Log::error('获取未处理分镜ID失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取生成任务状态
     */
    public function getGenerationTaskStatus(string $taskId, int $userId): array
    {
        try {
            // 查询包含该任务ID的分镜
            $storyboards = ProjectStoryboard::whereJsonContains('metadata->image_generation_result->task_id', $taskId)
                ->orWhereJsonContains('metadata->image_generation_task_id', $taskId)
                ->get();

            if ($storyboards->isEmpty()) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            // 验证用户权限（检查第一个分镜的项目权限）
            $firstStoryboard = $storyboards->first();
            $projectCheck = $this->checkProjectAccess($userId, $firstStoryboard->project_id);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $projectCheck;
            }

            // 统计任务状态
            $totalCount = $storyboards->count();
            $completedCount = $storyboards->where('status', ProjectStoryboard::STATUS_COMPLETED)->count();
            $failedCount = $storyboards->where('status', ProjectStoryboard::STATUS_FAILED)->count();
            $generatingCount = $storyboards->where('status', ProjectStoryboard::STATUS_GENERATING)->count();

            // 计算进度
            $progress = $totalCount > 0 ? (($completedCount + $failedCount) / $totalCount) * 100 : 0;

            // 确定整体状态
            $overallStatus = 'processing';
            if ($generatingCount === 0) {
                if ($failedCount === $totalCount) {
                    $overallStatus = 'failed';
                } elseif ($completedCount + $failedCount === $totalCount) {
                    $overallStatus = 'completed';
                }
            }

            // 构建结果数据
            $resultData = [
                'task_id' => $taskId,
                'status' => $overallStatus,
                'progress' => round($progress, 2),
                'summary' => [
                    'total_storyboards' => $totalCount,
                    'completed' => $completedCount,
                    'failed' => $failedCount,
                    'generating' => $generatingCount,
                    'pending' => $totalCount - $completedCount - $failedCount - $generatingCount
                ],
                'storyboards' => $storyboards->map(function ($storyboard) {
                    return [
                        'id' => $storyboard->id,
                        'status' => $storyboard->status,
                        'generated_image_id' => $storyboard->generated_image_id,
                        'metadata' => $storyboard->metadata['image_generation_result'] ?? null
                    ];
                })->toArray(),
                'updated_at' => Carbon::now('Asia/Shanghai')->format('c')
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取任务状态成功',
                'data' => $resultData
            ];

        } catch (\Exception $e) {
            Log::error('获取生成任务状态失败', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务状态失败',
                'data' => []
            ];
        }
    }

    /**
     * 从AI返回的JSON中提取角色信息
     * 遍历所有分镜，收集并去重角色信息
     */
    private function extractCharactersFromAiResponse(array $data): array
    {
        $characterStats = [];

        foreach ($data as $key => $value) {
            if (preg_match('/^场景(\d+)$/', $key) && is_array($value)) {
                if (isset($value['分镜']) && is_array($value['分镜'])) {
                    foreach ($value['分镜'] as $storyboard) {
                        if (isset($storyboard['出境角色'])) {
                            $characters = $this->parseCharacterNames($storyboard['出境角色']);

                            foreach ($characters as $character) {
                                // 统计角色出现次数
                                if (!isset($characterStats[$character])) {
                                    $characterStats[$character] = [
                                        'name' => $character,
                                        'appearance_count' => 0,
                                        'scenes' => [],
                                        'storyboards' => []
                                    ];
                                }

                                $characterStats[$character]['appearance_count']++;
                                $characterStats[$character]['scenes'][] = $key;
                                $characterStats[$character]['storyboards'][] = $storyboard['分镜序号'] ?? 0;
                            }
                        }
                    }
                }
            }
        }

        // 转换为数组并按出现频率排序
        $characters = array_values($characterStats);
        usort($characters, function($a, $b) {
            return $b['appearance_count'] <=> $a['appearance_count'];
        });

        // 为角色分配重要性等级
        foreach ($characters as &$character) {
            if ($character['appearance_count'] >= 5) {
                $character['importance'] = 'high';
                $character['role_type'] = 'protagonist';
            } elseif ($character['appearance_count'] >= 3) {
                $character['importance'] = 'medium';
                $character['role_type'] = 'supporting';
            } else {
                $character['importance'] = 'low';
                $character['role_type'] = 'minor';
            }

            // 去重场景和分镜数组
            $character['scenes'] = array_unique($character['scenes']);
            $character['storyboards'] = array_unique($character['storyboards']);
        }

        return $characters;
    }

    /**
     * 批量创建项目角色记录（先删除后创建策略）
     */
    private function batchCreateProjectCharacters(int $projectId, array $charactersData): array
    {
        // 先删除该项目的所有现有角色记录
        ProjectCharacter::where('project_id', $projectId)->delete();

        $createdCharacters = [];

        // 重新创建所有角色记录
        foreach ($charactersData as $characterData) {
            $character = ProjectCharacter::create([
                'project_id' => $projectId,
                'name' => $characterData['name'],
                'description' => "从故事中提取的角色，出现 {$characterData['appearance_count']} 次",
                'role_type' => $characterData['role_type'],
                'importance' => $characterData['importance'],
                'binding_status' => ProjectCharacter::STATUS_UNBOUND,
                'character_library_id' => null,
                'extracted_info' => [
                    'appearance_count' => $characterData['appearance_count'],
                    'scenes' => $characterData['scenes'],
                    'storyboards' => $characterData['storyboards'],
                    'extracted_from_ai' => true,
                    'extraction_time' => Carbon::now('Asia/Shanghai')->format('c')
                ]
            ]);

            $createdCharacters[] = $character;
        }

        return $createdCharacters;
    }
}
