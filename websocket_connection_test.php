<?php
/**
 * WebSocket连接测试脚本
 * 用于测试WebSocket服务器的连接数限制和资源清理
 */

require_once 'php/api/vendor/autoload.php';

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WebSocketConnectionTest
{
    private $testResults = [];
    
    public function runTests()
    {
        echo "🚀 开始WebSocket连接测试...\n\n";
        
        // 测试1：检查缓存一致性
        $this->testCacheConsistency();
        
        // 测试2：检查连接数统计
        $this->testConnectionCounting();
        
        // 测试3：检查清理机制
        $this->testCleanupMechanism();
        
        // 测试4：检查心跳缓存
        $this->testHeartbeatCache();
        
        // 输出测试结果
        $this->outputResults();
    }
    
    /**
     * 测试缓存一致性
     */
    private function testCacheConsistency()
    {
        echo "📋 测试1: 缓存一致性检查\n";
        
        try {
            // 模拟创建连接映射缓存
            $sessionId = 'test_session_' . time();
            $fd = 12345;
            
            // 使用修复后的统一过期时间（1800秒）
            Cache::put("websocket_fd_{$fd}", $sessionId, 1800);
            Cache::put("websocket_session_{$sessionId}", $fd, 1800);
            Cache::put("websocket_heartbeat_{$fd}", [
                'session_id' => $sessionId,
                'ping_at' => time(),
                'user_id' => 1
            ], 1800);
            
            // 检查缓存是否都存在
            $fdCache = Cache::has("websocket_fd_{$fd}");
            $sessionCache = Cache::has("websocket_session_{$sessionId}");
            $heartbeatCache = Cache::has("websocket_heartbeat_{$fd}");
            
            if ($fdCache && $sessionCache && $heartbeatCache) {
                $this->testResults['cache_consistency'] = '✅ 通过 - 缓存过期时间已统一';
                echo "   ✅ 缓存过期时间统一设置为1800秒\n";
            } else {
                $this->testResults['cache_consistency'] = '❌ 失败 - 缓存设置不一致';
                echo "   ❌ 缓存设置存在问题\n";
            }
            
            // 清理测试缓存
            Cache::forget("websocket_fd_{$fd}");
            Cache::forget("websocket_session_{$sessionId}");
            Cache::forget("websocket_heartbeat_{$fd}");
            
        } catch (\Exception $e) {
            $this->testResults['cache_consistency'] = '❌ 异常 - ' . $e->getMessage();
            echo "   ❌ 测试异常: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * 测试连接数统计
     */
    private function testConnectionCounting()
    {
        echo "📊 测试2: 连接数统计检查\n";
        
        try {
            // 检查配置
            $maxConnections = config('websocket.performance.max_connection', 1000);
            echo "   📋 最大连接数配置: {$maxConnections}\n";
            
            // 检查数据库中的活跃连接
            $activeConnections = DB::table('websocket_sessions')
                ->where('status', 'connected')
                ->count();
            
            echo "   📊 数据库活跃连接数: {$activeConnections}\n";
            
            // 检查缓存中的连接映射
            $cacheKeys = Cache::getRedis()->keys('websocket_session_*');
            $cacheConnections = count($cacheKeys);
            echo "   💾 缓存连接映射数: {$cacheConnections}\n";
            
            if ($activeConnections <= $maxConnections) {
                $this->testResults['connection_counting'] = '✅ 通过 - 连接数在限制范围内';
            } else {
                $this->testResults['connection_counting'] = '⚠️ 警告 - 连接数接近或超过限制';
            }
            
        } catch (\Exception $e) {
            $this->testResults['connection_counting'] = '❌ 异常 - ' . $e->getMessage();
            echo "   ❌ 测试异常: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * 测试清理机制
     */
    private function testCleanupMechanism()
    {
        echo "🧹 测试3: 清理机制检查\n";
        
        try {
            // 检查超时会话
            $timeoutSessions = DB::table('websocket_sessions')
                ->where('status', 'connected')
                ->where('last_ping_at', '<', now()->subMinutes(5))
                ->count();
            
            echo "   ⏰ 超时会话数量: {$timeoutSessions}\n";
            
            // 检查断开连接的会话
            $disconnectedSessions = DB::table('websocket_sessions')
                ->where('status', 'disconnected')
                ->where('disconnected_at', '>', now()->subHours(1))
                ->count();
            
            echo "   🔌 近1小时断开的会话: {$disconnectedSessions}\n";
            
            if ($timeoutSessions < 10) {
                $this->testResults['cleanup_mechanism'] = '✅ 通过 - 清理机制工作正常';
            } else {
                $this->testResults['cleanup_mechanism'] = '⚠️ 警告 - 存在较多超时会话，清理可能不及时';
            }
            
        } catch (\Exception $e) {
            $this->testResults['cleanup_mechanism'] = '❌ 异常 - ' . $e->getMessage();
            echo "   ❌ 测试异常: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * 测试心跳缓存
     */
    private function testHeartbeatCache()
    {
        echo "💓 测试4: 心跳缓存检查\n";
        
        try {
            // 检查心跳缓存数量
            $heartbeatKeys = Cache::getRedis()->keys('websocket_heartbeat_*');
            $heartbeatCount = count($heartbeatKeys);
            
            echo "   💓 心跳缓存数量: {$heartbeatCount}\n";
            
            // 检查是否有过期的心跳缓存
            $expiredCount = 0;
            foreach ($heartbeatKeys as $key) {
                $data = Cache::get(str_replace('laravel_database_', '', $key));
                if ($data && isset($data['ping_at'])) {
                    $pingTime = $data['ping_at'];
                    if ((time() - $pingTime) > 1800) { // 30分钟
                        $expiredCount++;
                    }
                }
            }
            
            echo "   ⏰ 过期心跳缓存: {$expiredCount}\n";
            
            if ($expiredCount < 5) {
                $this->testResults['heartbeat_cache'] = '✅ 通过 - 心跳缓存管理正常';
            } else {
                $this->testResults['heartbeat_cache'] = '⚠️ 警告 - 存在较多过期心跳缓存';
            }
            
        } catch (\Exception $e) {
            $this->testResults['heartbeat_cache'] = '❌ 异常 - ' . $e->getMessage();
            echo "   ❌ 测试异常: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * 输出测试结果
     */
    private function outputResults()
    {
        echo "📋 测试结果汇总:\n";
        echo "================\n";
        
        foreach ($this->testResults as $test => $result) {
            echo "• {$test}: {$result}\n";
        }
        
        echo "\n🔧 修复建议:\n";
        echo "1. 重启WebSocket服务器以应用修复\n";
        echo "2. 监控连接数使用率，保持在80%以下\n";
        echo "3. 定期检查日志中的清理统计信息\n";
        echo "4. 如果问题持续，考虑调整清理频率\n";
    }
}

// 运行测试
$test = new WebSocketConnectionTest();
$test->runTests();
