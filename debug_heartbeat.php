<?php
/**
 * WebSocket心跳调试脚本
 * 检查心跳缓存和数据库状态不一致的问题
 */

// 数据库连接配置
$host = '127.0.0.1';
$dbname = 'ai_tool';
$username = 'root';
$password = 'rootroot';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔍 WebSocket心跳调试分析\n";
    echo "============================\n\n";
    
    // 1. 检查数据库中的活跃会话
    echo "📊 数据库活跃会话状态:\n";
    $stmt = $pdo->query("SELECT session_id, user_id, status, connected_at, last_ping_at, 
                         TIMESTAMPDIFF(SECOND, last_ping_at, NOW()) as seconds_since_ping
                         FROM p_websocket_sessions 
                         WHERE status = 'connected' 
                         ORDER BY last_ping_at DESC");
    $activeSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($activeSessions)) {
        echo "   ❌ 数据库中没有活跃会话\n";
    } else {
        foreach ($activeSessions as $session) {
            $sessionId = substr($session['session_id'], 0, 25) . '...';
            $lastPing = $session['last_ping_at'] ? date('H:i:s', strtotime($session['last_ping_at'])) : 'N/A';
            $secondsSincePing = $session['seconds_since_ping'] ?? 'N/A';
            
            echo "   • {$sessionId} | 用户{$session['user_id']} | 最后心跳:{$lastPing} | 距今:{$secondsSincePing}秒\n";
        }
    }
    
    // 2. 模拟检查缓存中的心跳数据（需要Redis连接）
    echo "\n💾 缓存心跳数据检查:\n";
    
    // 连接Redis
    try {
        $redis = new Redis();
        $redis->connect('127.0.0.1', 6379);
        $redis->select(1); // 选择缓存数据库

        echo "   ✅ Redis连接成功（数据库1）\n";

        // 先检查所有websocket相关的键
        $allWebsocketKeys = $redis->keys('*websocket*');
        echo "   📋 所有websocket相关键: " . count($allWebsocketKeys) . " 个\n";

        if (!empty($allWebsocketKeys)) {
            foreach (array_slice($allWebsocketKeys, 0, 10) as $key) {
                echo "   • {$key}\n";
            }
            if (count($allWebsocketKeys) > 10) {
                echo "   • ... 还有 " . (count($allWebsocketKeys) - 10) . " 个键\n";
            }
        }

        // 获取所有心跳缓存键
        $heartbeatKeys = $redis->keys('*websocket_heartbeat_*');
        
        if (empty($heartbeatKeys)) {
            echo "   ❌ 缓存中没有心跳数据\n";
        } else {
            echo "   📋 找到 " . count($heartbeatKeys) . " 个心跳缓存:\n";

            $cacheSessionIds = [];
            foreach ($heartbeatKeys as $key) {
                $data = $redis->get($key);
                if ($data) {
                    // 尝试不同的反序列化方法
                    $cacheData = null;
                    if (substr($data, 0, 2) === 's:') {
                        // Laravel序列化格式
                        $cacheData = unserialize($data);
                    } else {
                        // JSON格式
                        $cacheData = json_decode($data, true);
                    }

                    if ($cacheData) {
                        // 详细分析缓存数据结构
                        echo "   🔍 键 {$key} 数据结构分析:\n";
                        foreach ($cacheData as $field => $value) {
                            echo "     • {$field}: " . (is_scalar($value) ? $value : gettype($value)) . "\n";
                        }

                        // 检查是否有session_id字段（可能是拼写错误）
                        $sessionIdField = null;
                        if (isset($cacheData['session_id'])) {
                            $sessionIdField = 'session_id';
                        } elseif (isset($cacheData['sessioon_id'])) {
                            $sessionIdField = 'sessioon_id';
                            echo "     ⚠️ 发现拼写错误的字段: sessioon_id\n";
                        }

                        if ($sessionIdField && isset($cacheData['ping_at'])) {
                            $fd = preg_replace('/.*websocket_heartbeat_/', '', $key);
                            $sessionId = substr($cacheData[$sessionIdField], 0, 25) . '...';
                            $pingTime = date('H:i:s', $cacheData['ping_at']);
                            $secondsAgo = time() - $cacheData['ping_at'];

                            echo "   ✅ FD:{$fd} | {$sessionId} | 用户{$cacheData['user_id']} | 心跳:{$pingTime} | 距今:{$secondsAgo}秒\n";

                            // 记录session_id用于后续分析
                            $cacheSessionIds[] = $cacheData[$sessionIdField];
                        } else {
                            echo "   ❌ 缺少必要字段 (session_id 或 ping_at)\n";
                        }
                    } else {
                        echo "   ⚠️ 键 {$key} 数据格式异常: " . substr($data, 0, 100) . "\n";
                    }
                } else {
                    echo "   ⚠️ 键 {$key} 无数据\n";
                }
            }
            
            // 3. 检查session_id重复问题
            echo "\n🔍 Session ID重复检查:\n";
            $sessionCounts = array_count_values($cacheSessionIds);
            $duplicates = array_filter($sessionCounts, function($count) { return $count > 1; });
            
            if (empty($duplicates)) {
                echo "   ✅ 没有重复的session_id\n";
            } else {
                echo "   ⚠️ 发现重复的session_id:\n";
                foreach ($duplicates as $sessionId => $count) {
                    $shortId = substr($sessionId, 0, 25) . '...';
                    echo "   • {$shortId}: {$count} 个连接\n";
                }
            }
            
            // 4. 检查缓存与数据库的一致性
            echo "\n🔄 缓存与数据库一致性检查:\n";
            $dbSessionIds = array_column($activeSessions, 'session_id');
            
            $cacheOnly = array_diff($cacheSessionIds, $dbSessionIds);
            $dbOnly = array_diff($dbSessionIds, $cacheSessionIds);
            
            if (empty($cacheOnly) && empty($dbOnly)) {
                echo "   ✅ 缓存与数据库完全一致\n";
            } else {
                if (!empty($cacheOnly)) {
                    echo "   ⚠️ 只在缓存中存在的session:\n";
                    foreach ($cacheOnly as $sessionId) {
                        $shortId = substr($sessionId, 0, 25) . '...';
                        echo "   • {$shortId}\n";
                    }
                }
                
                if (!empty($dbOnly)) {
                    echo "   ⚠️ 只在数据库中存在的session:\n";
                    foreach ($dbOnly as $sessionId) {
                        $shortId = substr($sessionId, 0, 25) . '...';
                        echo "   • {$shortId}\n";
                    }
                }
            }
        }
        
        $redis->close();
        
    } catch (Exception $e) {
        echo "   ❌ Redis连接失败: " . $e->getMessage() . "\n";
    }
    
    // 5. 分析可能的问题
    echo "\n🔧 问题分析:\n";
    
    $dbActiveCount = count($activeSessions);
    $cacheActiveCount = isset($heartbeatKeys) ? count($heartbeatKeys) : 0;
    
    echo "   • 数据库活跃会话数: {$dbActiveCount}\n";
    echo "   • 缓存心跳数据数: {$cacheActiveCount}\n";
    
    if ($dbActiveCount != $cacheActiveCount) {
        echo "   ⚠️ 数据库与缓存数量不一致，可能存在同步问题\n";
    }
    
    if (!empty($duplicates)) {
        echo "   ⚠️ 存在重复session_id，这会导致批量更新时覆盖\n";
    }
    
    // 检查是否有会话状态不是connected
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM p_websocket_sessions WHERE session_id IN ('" . 
                       implode("','", array_map(function($s) use ($pdo) { 
                           return $pdo->quote($s); 
                       }, $cacheSessionIds ?? [])) . "') AND status != 'connected'");
    $nonConnectedCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
    
    if ($nonConnectedCount > 0) {
        echo "   ⚠️ 发现 {$nonConnectedCount} 个缓存中的session在数据库中状态不是'connected'\n";
    }
    
    // 6. 建议修复方案
    echo "\n💡 修复建议:\n";
    
    if ($dbActiveCount != $cacheActiveCount) {
        echo "   • 清理不一致的缓存数据\n";
    }
    
    if (!empty($duplicates)) {
        echo "   • 检查连接建立逻辑，避免session_id重复\n";
    }
    
    if ($nonConnectedCount > 0) {
        echo "   • 清理状态不一致的会话记录\n";
    }
    
    echo "   • 增加更详细的调试日志来跟踪批量更新过程\n";
    
    echo "\n✅ 调试完成 - " . date('Y-m-d H:i:s') . "\n";
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ 调试失败: " . $e->getMessage() . "\n";
}
