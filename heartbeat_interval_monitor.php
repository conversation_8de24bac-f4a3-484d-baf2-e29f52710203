<?php
/**
 * 心跳间隔监控脚本
 * 监控WebSocket心跳的时间间隔是否正常
 */

// 数据库连接配置
$host = '127.0.0.1';
$dbname = 'ai_tool';
$username = 'root';
$password = 'rootroot';

try {
    echo "🔍 WebSocket心跳间隔监控\n";
    echo "============================\n\n";
    
    // 连接Redis查看心跳缓存
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    $redis->select(1); // 选择缓存数据库
    
    // 获取所有心跳缓存键
    $heartbeatKeys = $redis->keys('*websocket_heartbeat_*');
    
    if (!empty($heartbeatKeys)) {
        echo "📋 当前活跃连接心跳状态:\n";
        
        $heartbeatData = [];
        foreach ($heartbeatKeys as $key) {
            $data = $redis->get($key);
            if ($data) {
                $cacheData = unserialize($data);
                if ($cacheData && isset($cacheData['session_id']) && isset($cacheData['ping_at'])) {
                    $fd = preg_replace('/.*websocket_heartbeat_/', '', $key);
                    $heartbeatData[$fd] = [
                        'session_id' => $cacheData['session_id'],
                        'ping_at' => $cacheData['ping_at'],
                        'user_id' => $cacheData['user_id']
                    ];
                }
            }
        }
        
        // 显示当前心跳状态
        foreach ($heartbeatData as $fd => $data) {
            $sessionId = substr($data['session_id'], 0, 25) . '...';
            $pingTime = date('H:i:s', $data['ping_at']);
            $secondsAgo = time() - $data['ping_at'];
            
            echo "   • FD:{$fd} | {$sessionId} | 用户{$data['user_id']} | 最后心跳:{$pingTime} | 距今:{$secondsAgo}秒\n";
        }
        
        echo "\n🔄 开始监控心跳间隔（按Ctrl+C停止）...\n";
        echo "预期间隔：30秒\n\n";
        
        // 记录初始状态
        $previousHeartbeats = $heartbeatData;
        
        // 监控循环
        $monitorCount = 0;
        while ($monitorCount < 20) { // 监控20次，大约10分钟
            sleep(30); // 等待30秒
            $monitorCount++;
            
            echo "📊 第{$monitorCount}次检查 (" . date('H:i:s') . "):\n";
            
            // 获取新的心跳数据
            $currentHeartbeats = [];
            foreach ($heartbeatKeys as $key) {
                $data = $redis->get($key);
                if ($data) {
                    $cacheData = unserialize($data);
                    if ($cacheData && isset($cacheData['session_id']) && isset($cacheData['ping_at'])) {
                        $fd = preg_replace('/.*websocket_heartbeat_/', '', $key);
                        $currentHeartbeats[$fd] = [
                            'session_id' => $cacheData['session_id'],
                            'ping_at' => $cacheData['ping_at'],
                            'user_id' => $cacheData['user_id']
                        ];
                    }
                }
            }
            
            // 比较心跳间隔
            foreach ($currentHeartbeats as $fd => $currentData) {
                if (isset($previousHeartbeats[$fd])) {
                    $previousPing = $previousHeartbeats[$fd]['ping_at'];
                    $currentPing = $currentData['ping_at'];
                    $interval = $currentPing - $previousPing;
                    
                    $sessionId = substr($currentData['session_id'], 0, 20) . '...';
                    $status = '';
                    
                    if ($interval == 0) {
                        $status = '⚠️ 无变化';
                    } elseif ($interval >= 25 && $interval <= 35) {
                        $status = '✅ 正常';
                    } elseif ($interval < 25) {
                        $status = '🚨 过快';
                    } else {
                        $status = '⚠️ 过慢';
                    }
                    
                    echo "   FD:{$fd} | {$sessionId} | 间隔:{$interval}秒 | {$status}\n";
                } else {
                    echo "   FD:{$fd} | 新连接\n";
                }
            }
            
            // 检查是否有连接断开
            foreach ($previousHeartbeats as $fd => $data) {
                if (!isset($currentHeartbeats[$fd])) {
                    $sessionId = substr($data['session_id'], 0, 20) . '...';
                    echo "   FD:{$fd} | {$sessionId} | 🔴 连接已断开\n";
                }
            }
            
            $previousHeartbeats = $currentHeartbeats;
            echo "\n";
        }
        
    } else {
        echo "❌ 当前没有活跃的心跳缓存\n";
    }
    
    $redis->close();
    
} catch (Exception $e) {
    echo "❌ 监控失败: " . $e->getMessage() . "\n";
}
